<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dokumentácia skriptu camarilla_futures.py</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #3498db;
        }
        code {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            padding: 2px 4px;
            font-size: 0.9em;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            overflow-x: auto;
        }
        pre code {
            border: none;
            padding: 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .toc {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .note {
            background-color: #e7f5fe;
            border-left: 4px solid #3498db;
            padding: 10px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fff8e6;
            border-left: 4px solid #f39c12;
            padding: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>Dokumentácia skriptu camarilla_futures.py</h1>

    <div class="toc">
        <h2>Obsah</h2>
        <ul>
            <li><a href="#uvod">1. Úvod</a></li>
            <li><a href="#obchodna-strategia">2. Obchodná stratégia</a></li>
            <li><a href="#technicka-implementacia">3. Technická implementácia</a></li>
            <li><a href="#konfiguracia">4. Konfigurácia</a></li>
            <li><a href="#hlavne-komponenty">5. Hlavné komponenty</a></li>
            <li><a href="#obchodna-logika">6. Obchodná logika</a></li>
            <li><a href="#trailing-stop">7. Trailing Stop s offsetom</a></li>
            <li><a href="#market-close">8. Uzavretie pozícií pred koncom obchodovania</a></li>
            <li><a href="#instalacia">9. Inštalácia a spustenie</a></li>
            <li><a href="#monitoring">10. Monitoring a údržba</a></li>
            <li><a href="#riesenie-problemov">11. Riešenie problémov</a></li>
        </ul>
    </div>

    <h2 id="uvod">1. Úvod</h2>
    <p>
        Skript <code>camarilla_futures.py</code> je automatizovaný obchodný bot, ktorý implementuje Camarilla breakout stratégiu na futures kontraktoch MES (Micro E-mini S&P 500), MGC (Micro Gold), MNQ (Micro Nasdaq-100), M6A (Micro AUD/USD), M6B (Micro GBP/USD), M2K (Micro Russell 2000) a MBT (Micro Bitcoin). Bot sa pripája k Interactive Brokers (IB) pomocou knižnice ib_insync a vykonáva obchody na základe prelomenia Camarilla úrovní H4 a L4.
    </p>
    <p>
        Bot je navrhnutý tak, aby fungoval na 1-hodinovom časovom rámci a používa trailing stop s offsetom pre riadenie rizika a maximalizáciu zisku. Implementácia je inšpirovaná stratégiou z TradingView, ktorá používa funkcie <code>strategy.exit()</code> s parametrami <code>trail_points</code> a <code>trail_offset</code>.
    </p>
    <p>
        Hlavné funkcie bota:
    </p>
    <ul>
        <li>Obchodovanie na základe prelomenia Camarilla úrovní H4 a L4</li>
        <li>Trailing stop s offsetom pre riadenie rizika a maximalizáciu zisku</li>
        <li>Použitie ASK dát s useRTH=False pre presný výpočet Camarilla úrovní pre futures kontrakty</li>
        <li>Automatické uzatváranie pozícií 5 minút pred koncom obchodovania</li>
        <li>Podpora rôznych futures kontraktov s rôznymi tick size</li>
        <li>Telegram notifikácie o obchodoch a udalostiach</li>
    </ul>

    <h2 id="obchodna-strategia">2. Obchodná stratégia</h2>
    <p>
        Camarilla stratégia je založená na koncepte, že cena má tendenciu vracať sa k svojmu priemeru (mean reversion), ale keď prelomí určité úrovne, môže pokračovať v trende. Tento skript implementuje breakout verziu stratégie, ktorá obchoduje prelomenie Camarilla úrovní H4 a L4.
    </p>

    <h3>Signály pre vstup do obchodu:</h3>
    <ol>
        <li>
            <strong>LONG signál</strong>:
            <ul>
                <li>Cena uzavretia (close) je nad úrovňou H4</li>
                <li>Cena otvorenia (open) je pod úrovňou H4</li>
                <li>Cena uzavretia (close) je nad EMA(8)</li>
            </ul>
        </li>
        <li>
            <strong>SHORT signál</strong>:
            <ul>
                <li>Cena uzavretia (close) je pod úrovňou L4</li>
                <li>Cena otvorenia (open) je nad úrovňou L4</li>
                <li>Cena uzavretia (close) je pod EMA(8)</li>
            </ul>
        </li>
    </ol>

    <h3>Výpočet Camarilla úrovní:</h3>
    <p>
        Camarilla úrovne H4 a L4 sa počítajú z predchádzajúcej dennej sviečky:
    </p>
    <pre><code>def calc_pivots(df: pd.DataFrame):
    # Použijeme posledný kompletný deň (predposledný v datasete)
    if len(df) >= 2:
        idx = -2  # Predposledný deň (predchádzajúci obchodný deň)
    else:
        idx = -1  # Ak máme len jeden deň, použijeme ho

    ph, pl, pc = df['high'].iloc[idx], df['low'].iloc[idx], df['close'].iloc[idx]
    rng = ph - pl

    # Camarilla vzorec používa konštantu 0.55
    h4 = pc + rng * 0.55
    l4 = pc - rng * 0.55

    return h4, l4</code></pre>

    <h3>Riadenie rizika:</h3>
    <ol>
        <li>
            <strong>Stop-Loss</strong>:
            <ul>
                <li>Pre LONG pozície: 70 tickov pod vstupnou cenou</li>
                <li>Pre SHORT pozície: 40 tickov nad vstupnou cenou</li>
            </ul>
        </li>
        <li>
            <strong>Trailing Stop s offsetom</strong>:
            <ul>
                <li>Pre LONG pozície: 40 tickov trailing stop, aktivovaný 1 tick nad vstupnou cenou</li>
                <li>Pre SHORT pozície: 20 tickov trailing stop, aktivovaný 1 tick pod vstupnou cenou</li>
            </ul>
        </li>
    </ol>

    <div class="note">
        <p><strong>Poznámka:</strong> Hodnoty stop-loss a trailing stop sú definované v tickoch, nie v absolútnej cene. Bot automaticky konvertuje ticky na cenu podľa minimálneho cenového kroku (tick size) pre každý kontrakt.</p>
    </div>

    <h2 id="technicka-implementacia">3. Technická implementácia</h2>
    <p>
        Skript je napísaný v jazyku Python a používa nasledujúce knižnice:
    </p>
    <ul>
        <li><strong>ib_insync</strong>: Pripojenie k Interactive Brokers API</li>
        <li><strong>pandas</strong>: Spracovanie a analýza dát</li>
        <li><strong>datetime</strong>: Práca s časom a dátumami</li>
        <li><strong>requests</strong>: Odosielanie Telegram notifikácií</li>
        <li><strong>csv</strong>: Logovanie obchodov do CSV súboru</li>
    </ul>

    <h2 id="konfiguracia">4. Konfigurácia</h2>
    <p>
        Hlavné konfiguračné parametre skriptu:
    </p>
    <pre><code>TIMEFRAME = '1 hour'           # Časový rámec pre obchodovanie
QUANTITY = 1                   # Počet kontraktov na obchod

# Hodnoty pre Stop-Loss a Trailing Stop (v tickoch)
SL_PTS_LONG     = 70   # Stop-loss pre LONG pozície
TRAIL_PTS_LONG  = 40   # Trailing stop pre LONG pozície
TRAIL_OFFSET_LONG = 1  # Offset pre aktiváciu trailing stopu pre LONG

SL_PTS_SHORT    = 40   # Stop-loss pre SHORT pozície
TRAIL_PTS_SHORT = 20   # Trailing stop pre SHORT pozície
TRAIL_OFFSET_SHORT = 1 # Offset pre aktiváciu trailing stopu pre SHORT

# Definícia tick size pre rôzne kontrakty
tick_sizes = {
    'M6A': 0.00005,  # Micro AUD/USD - 0.5 pip
    'M6B': 0.0001,   # Micro GBP/USD - 1 pip
    'M6C': 0.00005,  # Micro CAD/USD - 0.5 pip
    'MES': 0.25,     # Micro E-mini S&P 500 - 0.25 point
    'MNQ': 0.25,     # Micro E-mini Nasdaq-100 - 0.25 point
    'M2K': 0.1,      # Micro E-mini Russell 2000 - 0.1 point
    'MBT': 5.0,      # Micro Bitcoin - 5 points
    'MGC': 0.1       # Micro Gold - 0.1 point
}

# Obchodované inštrumenty
INSTRUMENTS = [
    {'symbol': 'MES', 'exchange': 'CME',   'multiplier': 5, 'secType': 'FUTURE'},
    {'symbol': 'MGC', 'exchange': 'COMEX', 'multiplier': 10, 'secType': 'FUTURE'},
    {'symbol': 'MNQ', 'exchange': 'CME',   'multiplier': 2, 'secType': 'FUTURE'},
    {'symbol': 'M6A', 'exchange': 'CME',   'multiplier': 10000, 'secType': 'FUTURE'},
    {'symbol': 'M6B', 'exchange': 'CME',   'multiplier': 12500, 'secType': 'FUTURE'},
    {'symbol': 'M2K', 'exchange': 'CME',   'multiplier': 5, 'secType': 'FUTURE'},
    {'symbol': 'MBT', 'exchange': 'CME',   'multiplier': 0.1, 'secType': 'FUTURE'},
]

# Telegram notifikácie
TELEGRAM_TOKEN = '<YOUR_TELEGRAM_TOKEN>'
TELEGRAM_CHAT_ID = '<YOUR_CHAT_ID>'

# Nastavenie času pre uzavretie pozícií pred koncom obchodovania
MARKET_CLOSE_TIME = "17:00"  # Čas uzavretia trhu v ET (Eastern Time)
CLOSE_POSITIONS_MINUTES_BEFORE = 5  # Uzavrieť pozície 5 minút pred koncom obchodovania</code></pre>

    <h2 id="hlavne-komponenty">5. Hlavné komponenty</h2>

    <h3>1. Inicializácia a pripojenie k IB</h3>
    <pre><code>def main_loop():
    ib.connect('127.0.0.1', 4001, clientId=1)
    logging.info("Connected to IB Gateway on EH port 4001")</code></pre>

    <h3>2. Získavanie kontraktov</h3>
    <pre><code>def fetch_contract(symbol, exchange, expiry=None, secType='FUTURE'):
    # Implementácia získavania kontraktov z IB</code></pre>

    <h3>3. Výpočet Camarilla úrovní</h3>
    <pre><code>def calc_pivots(df: pd.DataFrame):
    ph, pl, pc = df['high'].iloc[-2], df['low'].iloc[-2], df['close'].iloc[-2]
    rng = ph - pl
    h4 = pc + rng * 1.1/2
    l4 = pc - rng * 1.1/2
    return h4, l4</code></pre>

    <h3>4. Čakanie na ďalšiu sviečku</h3>
    <pre><code>def wait_for_next_bar(tf_minutes: int):
    # Implementácia čakania na ďalšiu sviečku podľa časového rámca</code></pre>

    <h3>5. Bracket objednávky (vstup + stop-loss)</h3>
    <pre><code>def place_bracket(inst, signal, entry_price):
    # Implementácia bracket objednávok</code></pre>

    <h3>6. Telegram notifikácie</h3>
    <pre><code>def send_telegram(message: str):
    url = f"https://api.telegram.org/bot<YOUR_TELEGRAM_TOKEN>/sendMessage"
    payload = {'chat_id': '<YOUR_CHAT_ID>', 'text': message}
    try:
        requests.post(url, data=payload)
    except Exception as e:
        logging.error(f"Telegram notification failed: {e}")</code></pre>

    <h3>7. Logovanie obchodov</h3>
    <pre><code>def append_trade_to_csv(symbol, signal, entry_price, exit_price, pnl, entry_time, sl_level, trail_activated):
    # Implementácia logovania obchodov do CSV súboru</code></pre>

    <h2 id="obchodna-logika">6. Obchodná logika</h2>
    <p>
        Hlavná obchodná logika je implementovaná v hlavnej slučke skriptu:
    </p>
    <pre><code># Získanie historických dát
# Používame ASK s useRTH=False, pretože poskytuje najpresnejšie hodnoty pre futures kontrakty
daily = ib.reqHistoricalData(c, '', durationStr='5 D', barSizeSetting='1 day',
                           whatToShow='ASK', useRTH=False)

# Konverzia na DataFrame
df_d = util.df(daily)

# Logujeme získané denné dáta pre debugging s vhodným počtom desatinných miest
for i, row in df_d.iterrows():
    # Formátovanie dátumu
    if hasattr(i, 'strftime'):
        date_str = i.strftime('%Y-%m-%d')
    else:
        date_str = str(i)[:10]  # Berieme prvých 10 znakov, čo by malo byť YYYY-MM-DD

    # Formátovanie s vhodným počtom desatinných miest podľa symbolu
    if inst['symbol'] in ['M6A', 'M6B']:
        # Pre menové páry používame 4 desatinné miesta
        logging.info(f"Daily bar {i}: date={date_str} open={row['open']:.4f} high={row['high']:.4f} low={row['low']:.4f} close={row['close']:.4f}")
    else:
        # Pre ostatné kontrakty používame 2 desatinné miesta
        logging.info(f"Daily bar {i}: date={date_str} open={row['open']:.2f} high={row['high']:.2f} low={row['low']:.2f} close={row['close']:.2f}")

# Výpočet Camarilla úrovní
h4_d, l4_d = calc_pivots(df_d)

# Získanie dát pre aktuálny časový rámec
# Používame ASK s useRTH=False, rovnako ako pre denné dáta
bars = ib.reqHistoricalData(c, '', durationStr='1 D', barSizeSetting=TIMEFRAME,
                           whatToShow='ASK', useRTH=False)
df = util.df(bars)
df['ema8'] = df['close'].ewm(span=8, adjust=False).mean()

# Výpočet signálov
last = df.iloc[-1]
lc, lo, le = last['close'], last['open'], last['ema8']

# Formátovanie s vhodným počtom desatinných miest podľa symbolu
if inst['symbol'] in ['M6A', 'M6B']:
    # Pre menové páry používame 4 desatinné miesta
    logging.info(f"{inst['symbol']} last bar: O={lo:.4f} C={lc:.4f} ema8={le:.4f} h4_d={h4_d:.4f} l4_d={l4_d:.4f}")
else:
    # Pre ostatné kontrakty používame 2 desatinné miesta
    logging.info(f"{inst['symbol']} last bar: O={lo:.2f} C={lc:.2f} ema8={le:.2f} h4_d={h4_d:.2f} l4_d={l4_d:.2f}")
longSig  = lc > h4_d and lo < h4_d and lc > le
shortSig = lc < l4_d and lo > l4_d and lc < le

# Vstup do obchodu
if longSig and not inst['inPosition']:
    place_bracket(inst, 'LONG', lc)
    # Nastavenie parametrov pozície
elif shortSig and not inst['inPosition']:
    place_bracket(inst, 'SHORT', lc)
    # Nastavenie parametrov pozície</code></pre>

    <h2 id="trailing-stop">7. Trailing Stop s offsetom</h2>
    <p>
        Implementácia trailing stopu s offsetom je jednou z kľúčových funkcií skriptu. Funguje nasledovne:
    </p>
    <ol>
        <li>Pri vstupe do obchodu sa nastaví pevný stop-loss</li>
        <li>Bot sleduje cenu a keď dosiahne offset (1 tick od vstupnej ceny v smere obchodu), aktivuje trailing stop</li>
        <li>Trailing stop sleduje cenu s odstupom 40 tickov pre LONG a 20 tickov pre SHORT pozície</li>
    </ol>
    <pre><code># Získanie tick size pre daný kontrakt
tick_size = tick_sizes.get(sym, 1.0)  # Default 1.0 ak nie je definované

# Konverzia tickov na cenu
sl_pts_long_price = SL_PTS_LONG * tick_size
sl_pts_short_price = SL_PTS_SHORT * tick_size
trail_pts_long_price = TRAIL_PTS_LONG * tick_size
trail_pts_short_price = TRAIL_PTS_SHORT * tick_size
trail_offset_long_price = TRAIL_OFFSET_LONG * tick_size
trail_offset_short_price = TRAIL_OFFSET_SHORT * tick_size

# Nastavenie stop-loss ceny
sl_price = entry_price - sl_pts_long_price if signal == 'LONG' else entry_price + sl_pts_short_price

# Nastavenie aktivačnej ceny pre trailing stop
inst['trail_offset_reached'] = False
if signal == 'LONG':
    inst['trail_activation_price'] = entry_price + trail_pts_long_price  # Cena, pri ktorej sa aktivuje trailing stop
    inst['trail_offset'] = trail_offset_long_price  # Uložíme offset pre neskoršie použitie
else:  # SHORT
    inst['trail_activation_price'] = entry_price - trail_pts_short_price  # Cena, pri ktorej sa aktivuje trailing stop
    inst['trail_offset'] = trail_offset_short_price  # Uložíme offset pre neskoršie použitie

# Kontrola a aktivácia trailing stopu
if inst['inPosition'] and not inst['trailing_set']:
    current_price = get_current_price(inst['contract'])

    if inst['entry_signal'] == 'LONG' and current_price and current_price >= inst['trail_activation_price']:
        # Vytvoríme trailing stop order s uloženým offsetom
        trail = Order(orderType='TRAIL', action='SELL', totalQuantity=QUANTITY, auxPrice=inst['trail_offset'], transmit=True)
        trail.outsideRth = True
        ib.placeOrder(inst['contract'], trail)
        inst['trailing_set'] = True
        # Formátovanie s vhodným počtom desatinných miest podľa symbolu
        if inst['symbol'] in ['M6A', 'M6B']:
            # Pre menové páry používame 4 desatinné miesta
            msg = f"Trailing stop activated on {inst['symbol']} @ {current_price:.4f} with trail offset {inst['trail_offset']:.4f}"
        else:
            # Pre ostatné kontrakty používame 2 desatinné miesta
            msg = f"Trailing stop activated on {inst['symbol']} @ {current_price:.2f} with trail offset {inst['trail_offset']:.2f}"

        logging.info(msg)
        send_telegram(f"🔒 {msg}")
    elif inst['entry_signal'] == 'SHORT' and current_price and current_price <= inst['trail_activation_price']:
        # Vytvoríme trailing stop order s uloženým offsetom
        trail = Order(orderType='TRAIL', action='BUY', totalQuantity=QUANTITY, auxPrice=inst['trail_offset'], transmit=True)
        trail.outsideRth = True
        ib.placeOrder(inst['contract'], trail)
        inst['trailing_set'] = True
        # Formátovanie s vhodným počtom desatinných miest podľa symbolu
        if inst['symbol'] in ['M6A', 'M6B']:
            # Pre menové páry používame 4 desatinné miesta
            msg = f"Trailing stop activated on {inst['symbol']} @ {current_price:.4f} with trail offset {inst['trail_offset']:.4f}"
        else:
            # Pre ostatné kontrakty používame 2 desatinné miesta
            msg = f"Trailing stop activated on {inst['symbol']} @ {current_price:.2f} with trail offset {inst['trail_offset']:.2f}"

        logging.info(msg)
        send_telegram(f"🔒 {msg}")</code></pre>

    <h2 id="market-close">8. Uzavretie pozícií pred koncom obchodovania</h2>
    <p>
        Bot automaticky uzatvára všetky otvorené pozície 5 minút pred koncom obchodovania, aby sa vyhol držaniu pozícií cez noc. Táto funkcia je implementovaná nasledovne:
    </p>
    <pre><code># Kontrola času do konca obchodovania
current_time_et = datetime.now(timezone('US/Eastern'))
market_close_time_str = current_time_et.strftime('%Y-%m-%d ') + MARKET_CLOSE_TIME
market_close_time = datetime.strptime(market_close_time_str, '%Y-%m-%d %H:%M')
market_close_time = timezone('US/Eastern').localize(market_close_time)

# Výpočet času do konca obchodovania v minútach
minutes_to_close = (market_close_time - current_time_et).total_seconds() / 60

# Ak je čas do konca obchodovania menší ako nastavený limit, uzavrieme všetky pozície
if 0 < minutes_to_close < CLOSE_POSITIONS_MINUTES_BEFORE:
    for inst in INSTRUMENTS:
        if inst.get('inPosition', False):
            close_position(inst, "Market close approaching")
            msg = f"Closed position on {inst['symbol']} due to approaching market close"
            logging.info(msg)
            send_telegram(msg)</code></pre>

    <h2 id="instalacia">9. Inštalácia a spustenie</h2>

    <h3>Požiadavky:</h3>
    <ul>
        <li>Python 3.6+</li>
        <li>Interactive Brokers Gateway alebo TWS</li>
        <li>Knižnice: ib_insync, pandas, requests</li>
    </ul>

    <h3>Inštalácia knižníc:</h3>
    <pre><code>pip install ib_insync pandas requests</code></pre>

    <h3>Spustenie skriptu:</h3>
    <pre><code>python camarilla_futures.py</code></pre>

    <h3>Spustenie ako služba na Linux (systemd):</h3>
    <pre><code># Vytvorenie service súboru
sudo nano /etc/systemd/system/mesbot.service

# Obsah súboru
[Unit]
Description=MES Camarilla Bot
After=network.target

[Service]
ExecStart=/home/<USER>/mes_bot/venv/bin/python /home/<USER>/mes_bot/camarilla_futures.py
WorkingDirectory=/home/<USER>/mes_bot
User=trader
Group=trader
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target

# Aktivácia a spustenie služby
sudo systemctl enable mesbot.service
sudo systemctl start mesbot.service

# Sledovanie logov
journalctl -u mesbot.service -f</code></pre>

    <h2 id="monitoring">10. Monitoring a údržba</h2>

    <h3>Sledovanie logov:</h3>
    <p>
        Skript používa štandardný Python logging modul a zapisuje logy na štandardný výstup. Pri spustení ako systemd služba sa logy zapisujú do journald.
    </p>

    <h3>Telegram notifikácie:</h3>
    <p>
        Bot posiela notifikácie o obchodoch cez Telegram. Notifikácie zahŕňajú:
    </p>
    <ul>
        <li>Otvorenie pozície</li>
        <li>Aktivácia trailing stopu</li>
        <li>Uzavretie pozície s P/L</li>
    </ul>

    <h3>CSV logovanie:</h3>
    <p>
        Bot zapisuje všetky obchody do CSV súboru <code>trades_log.csv</code> s nasledujúcimi informáciami:
    </p>
    <ul>
        <li>Symbol</li>
        <li>Smer obchodu (LONG/SHORT)</li>
        <li>Vstupná cena</li>
        <li>Výstupná cena</li>
        <li>P/L</li>
        <li>Čas vstupu</li>
        <li>Úroveň stop-loss</li>
        <li>Či bol aktivovaný trailing stop</li>
    </ul>

    <h2 id="riesenie-problemov">11. Riešenie problémov</h2>

    <h3>Časté problémy:</h3>

    <ol>
        <li>
            <strong>Problém s pripojením k IB Gateway</strong>:
            <ul>
                <li>Skontrolujte, či je IB Gateway spustený a dostupný na porte 4001</li>
                <li>Skontrolujte, či je povolené API pripojenie v nastaveniach IB Gateway</li>
            </ul>
        </li>
        <li>
            <strong>Problém s kontraktmi</strong>:
            <ul>
                <li>Skontrolujte, či sú kontrakty MES a MGC dostupné</li>
                <li>Skontrolujte, či je správne nastavený expiry mesiac</li>
            </ul>
        </li>
        <li>
            <strong>Problém s dátami</strong>:
            <ul>
                <li>Skontrolujte, či máte predplatené dáta pre dané inštrumenty</li>
                <li>Skontrolujte, či sú dostupné historické dáta</li>
            </ul>
        </li>
        <li>
            <strong>Problém s obchodovaním</strong>:
            <ul>
                <li>Skontrolujte, či máte dostatočné prostriedky na účte</li>
                <li>Skontrolujte, či máte povolené obchodovanie futures kontraktov</li>
            </ul>
        </li>
    </ol>

    <h3>Riešenie:</h3>
    <ol>
        <li>Reštartujte IB Gateway</li>
        <li>Reštartujte bot pomocou <code>systemctl restart mesbot.service</code></li>
        <li>Skontrolujte logy pomocou <code>journalctl -u mesbot.service -f</code></li>
        <li>Upravte konfiguračné parametre podľa potreby</li>
    </ol>

    <div class="note">
        <p><strong>Poznámka:</strong> Tento bot je určený na obchodovanie futures kontraktov MES, MGC, MNQ, M6A, M6B, M2K a MBT, ktoré môžu vyžadovať platené live dáta. Uistite sa, že máte príslušné predplatné u Interactive Brokers.</p>
    </div>

    <div class="warning">
        <p><strong>Upozornenie:</strong> Obchodovanie futures kontraktov zahŕňa riziko straty. Tento bot je poskytovaný "tak ako je" bez akýchkoľvek záruk. Používajte ho na vlastné riziko a vždy testujte stratégiu na demo účte pred nasadením na reálny účet.</p>
    </div>

    <h3>Špecifické problémy a ich riešenia:</h3>

    <ol>
        <li>
            <strong>Problém so získavaním dát</strong>:
            <ul>
                <li>Bot používa ASK dáta s useRTH=False pre získavanie denných dát, pretože poskytujú najpresnejšie hodnoty pre futures kontrakty</li>
                <li>Ak sa vyskytne problém so získavaním dát, skontrolujte logy, či boli úspešne získané denné dáta</li>
            </ul>
        </li>
        <li>
            <strong>Problém s trailing stop hodnotami</strong>:
            <ul>
                <li>Bot konvertuje ticky na cenu podľa tick size pre každý kontrakt</li>
                <li>Ak sa vyskytne problém s trailing stop hodnotami, skontrolujte, či je správne definovaný tick size pre daný kontrakt</li>
            </ul>
        </li>
        <li>
            <strong>Problém s uzatváraním pozícií pred koncom obchodovania</strong>:
            <ul>
                <li>Bot automaticky uzatvára pozície 5 minút pred koncom obchodovania (17:00 ET)</li>
                <li>Ak sa pozície neuzavrú, skontrolujte, či je správne nastavený čas uzavretia trhu a časové pásmo</li>
            </ul>
        </li>
        <li>
            <strong>Problém s nepotvrdením pozície po zadaní príkazov</strong>:
            <ul>
                <li>Bot teraz kontroluje či bola pozícia skutočne otvorená po zadaní bracket order</li>
                <li>Ak sa pozícia nepotvrdí po 3 pokusoch (každý po 5 sekundách), bot ju nebude spravovať</li>
                <li>V logoch hľadajte správy typu "pozícia zatiaľ neviditeľná" alebo "pozícia NEBOLA potvrdená"</li>
                <li>Možné príčiny: neplatná SL cena, nedostatok margin, problém s parent order</li>
                <li>Riešenie: skontrolujte TWS manuálne, overte margin a SL ceny</li>
            </ul>
        </li>
        <li>
            <strong>SL order zrušený (Cancelled)</strong>:
            <ul>
                <li>Ak je SL order zrušený hneď po zadaní, môže to znamenať problém s parent order</li>
                <li>Skontrolujte či parent order bol úspešne vyplnený</li>
                <li>Overte či SL cena nie je príliš blízko k aktuálnej trhovej cene</li>
                <li>Bot automaticky zruší SL order ak pozícia nebola potvrdená</li>
            </ul>
        </li>
    </ol>
</body>
</html>
