#!/usr/bin/env python3
# camarilla.py

import logging
import time
from datetime import datetime, timedelta
from ib_insync import IB, util, Order, MarketOrder, Contract
import pandas as pd
import pytz
import sys
import os

import config
import utils
import ib_interface

logger = logging.getLogger(__name__)

ib = IB()
last_closed_conids_session = set()
current_active_expiry = None
instruments_data_list = []
theoretical_new_bar_start_utc = None
needs_data_initialization = True


def initialize_instruments_data():
    global instruments_data_list, current_active_expiry, ib, needs_data_initialization
    logger.info("Inicializujem/Reinicializujem dáta inštrumentov...")

    previous_positions_state = {
        inst_data.get('symbol'): {
            'inPosition': inst_data.get('inPosition', False),
            'entry_signal': inst_data.get('entry_signal', '')
        } for inst_data in instruments_data_list if inst_data.get('symbol')
    }

    temp_instruments_data_list = [] # Použijeme dočasný zoznam

    # NOVÁ LOGIKA: Nepoužívame už current_active_expiry pre všetky nástroje
    # Každý nástroj si vyberie svoj najlepší kontrakt individuálne
    if current_active_expiry is None:
        current_active_expiry = "DYNAMIC"  # Označenie, že používame dynamický výber
        logger.info("Používam dynamický výber najlepších kontraktov pre každý nástroj")

    if not config.INSTRUMENTS_CONFIG:
        logger.warning("Zoznam INSTRUMENTS_CONFIG v config.py je prázdny!")
        instruments_data_list = [] # Uistíme sa, že je prázdny
        needs_data_initialization = False
        return

    for inst_conf in config.INSTRUMENTS_CONFIG:
        inst_data_new = inst_conf.copy()
        inst_data_new.update({
            'contract': None, 'inPosition': False, 'entry_price': 0.0,
            'entry_signal': '', 'entry_time': '', 'entry_timestamp': 0.0,
            'sl_order_id': None, 'sl_price': 0.0, 'trailing_set': False,
            'trail_offset_reached': False, 'trail_activation_price': 0.0,
            'trail_offset': 0.0, 'last_bar_processed_utc': None
        })

        symbol = inst_data_new['symbol']
        try:
            logger.info(f"[{symbol}] Načítavam najlepší dostupný kontrakt...")
            # NOVÁ LOGIKA: Použiť novú funkciu pre výber najlepšieho kontraktu
            inst_data_new['contract'] = utils.get_best_contract_for_instrument(
                ib, symbol, inst_data_new['exchange'], inst_data_new['currency']
            )
            if inst_data_new['contract']:
                logger.info(f"[{symbol}] Kontrakt úspešne načítaný: {getattr(inst_data_new['contract'], 'localSymbol', symbol)}")
                if symbol in previous_positions_state and previous_positions_state[symbol]['inPosition']:
                    logger.warning(f"[{symbol}] Po (re)inicializácii kontraktov bol tento inštrument predtým v pozícii ({previous_positions_state[symbol]['entry_signal']}). "
                                   f"Interný stav 'inPosition' je teraz False. Nutná synchronizácia.")
            else:
                logger.error(f"[{symbol}] Nepodarilo sa načítať kontrakt pri (re)inicializácii.")
        except Exception as e_init_contract_loop:
            logger.error(f"[{symbol}] Chyba pri načítaní kontraktu pri (re)inicializácii: {e_init_contract_loop}", exc_info=True)

        temp_instruments_data_list.append(inst_data_new)

    instruments_data_list = temp_instruments_data_list # Priradíme až po úspešnom naplnení dočasného zoznamu
    valid_contracts_count = sum(1 for i in instruments_data_list if i.get('contract') is not None)
    logger.info(f"Inicializovaných/Reinicializovaných {len(instruments_data_list)} inštrumentov. Platných kontraktov: {valid_contracts_count}")
    needs_data_initialization = False


def safe_process_dataframe_index(df_input, symbol_for_log, data_type_log):
    if df_input is None or df_input.empty:
        logger.warning(f"[{symbol_for_log}] Prijatý prázdny alebo None DataFrame pre {data_type_log} dáta.")
        return None

    df = df_input.copy()

    if 'date' not in df.columns and not isinstance(df.index, pd.DatetimeIndex):
        logger.error(f"[{symbol_for_log}] Chýba stĺpec 'date' a index nie je DatetimeIndex v {data_type_log} dátach. Stĺpce: {list(df.columns)}")
        return None
    try:
        if 'date' in df.columns and not isinstance(df.index, pd.DatetimeIndex):
            # Overíme, či 'date' stĺpec nie je už náhodou DatetimeIndex (aj keď to nie je štandardné pre stĺpec)
            if pd.api.types.is_datetime64_any_dtype(df['date'].dtype):
                 df = df.set_index('date', drop=True)
            else: # Ak to nie je datetime, pokúsime sa konvertovať
                logger.debug(f"[{symbol_for_log}] Konvertujem stĺpec 'date' na datetime pre {data_type_log} dáta.")
                df['date'] = pd.to_datetime(df['date'], errors='coerce')
                df.dropna(subset=['date'], inplace=True)
                if df.empty:
                    logger.warning(f"[{symbol_for_log}] DataFrame je prázdny po odstránení neplatných dátumov v {data_type_log} dátach.")
                    return None
                df = df.set_index('date', drop=True)

        if not isinstance(df.index, pd.DatetimeIndex):
            logger.warning(f"[{symbol_for_log}] Index {data_type_log} dát nie je DatetimeIndex. Typ: {type(df.index)}. Skúšam pd.to_datetime(df.index).")
            try:
                original_index_name = df.index.name # Uložíme si názov indexu, ak existuje
                df.index = pd.to_datetime(df.index, errors='coerce')
                if original_index_name: df.index.name = original_index_name # Obnovíme názov

                if pd.NaT in df.index:
                    logger.warning(f"[{symbol_for_log}] Index obsahuje NaT hodnoty po konverzii, odstraňujem ich.")
                    df = df[df.index.notna()]
                if df.empty or not isinstance(df.index, pd.DatetimeIndex):
                     logger.error(f"[{symbol_for_log}] Index stále nie je DatetimeIndex ani po druhom pokuse alebo je prázdny.")
                     return None
            except Exception as e_conv_idx_direct:
                 logger.error(f"[{symbol_for_log}] Chyba pri druhom pokuse o konverziu indexu na DatetimeIndex: {e_conv_idx_direct}")
                 return None

        if df.index.tz is None: # Ak je naivný
            logger.debug(f"[{symbol_for_log}] {data_type_log} index je naivný, lokalizujem na UTC.")
            try:
                df.index = df.index.tz_localize(pytz.utc, ambiguous='infer', nonexistent='shift_forward')
            except Exception as e_tz_loc:
                logger.error(f"[{symbol_for_log}] Chyba pri tz_localize {data_type_log} na UTC: {e_tz_loc}. Dáta budú preskočené.")
                return None
        else: # Ak je už uvedomelý
            logger.debug(f"[{symbol_for_log}] {data_type_log} index je uvedomelý ({df.index.tz}), konvertujem na UTC.")
            df.index = df.index.tz_convert(pytz.utc)

        if df.empty:
            logger.warning(f"[{symbol_for_log}] DataFrame pre {data_type_log} je prázdny po spracovaní indexu.")
            return None

        return df
    except Exception as e_process_idx:
        logger.error(f"[{symbol_for_log}] Všeobecná chyba pri spracovaní indexu {data_type_log} dát: {e_process_idx}", exc_info=True)
        return None


def run_main_trading_loop():
    global last_closed_conids_session, current_active_expiry, instruments_data_list, ib
    global theoretical_new_bar_start_utc, needs_data_initialization

    if needs_data_initialization:
        logger.info("run_main_trading_loop: Potrebná (re)inicializácia dát inštrumentov.")
        initialize_instruments_data()

    active_instruments_to_process = [inst for inst in instruments_data_list if inst.get('contract')]
    if not active_instruments_to_process:
        logger.error("run_main_trading_loop: Žiadne platné inštrumenty s kontraktami na spracovanie. Čakám 60s.")
        needs_data_initialization = True
        time.sleep(60)
        return

    if theoretical_new_bar_start_utc is None:
        logger.info("run_main_trading_loop: Prvá barová iterácia, čakám na prvú celú sviečku...")

    theoretical_new_bar_start_utc = utils.wait_for_next_bar(config.TIMEFRAME)
    logger.info(f"Nová perióda začína. Teoretický začiatok novej sviečky (UTC): {theoretical_new_bar_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")

    tf_parts = config.TIMEFRAME.split()
    tf_value = int(tf_parts[0]) if len(tf_parts) > 0 and tf_parts[0].isdigit() else 1
    tf_unit = tf_parts[1].lower() if len(tf_parts) > 1 else "hour"
    if 'hour' in tf_unit: timeframe_delta = timedelta(hours=tf_value)
    elif 'min' in tf_unit: timeframe_delta = timedelta(minutes=tf_value)
    else: timeframe_delta = timedelta(hours=1); logger.error(f"Neznámy TIMEFRAME unit: {config.TIMEFRAME}")

    expected_signal_bar_start_utc = theoretical_new_bar_start_utc - timeframe_delta
    logger.info(f"Pre tento cyklus je očakávaný začiatok signálnej sviečky (UTC): {expected_signal_bar_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")

    # NOVÁ LOGIKA: Rollover sa už nerobí globálne, ale individuálne pre každý nástroj
    # Kontrola rolloveru sa presunie do samostatnej funkcie, ktorá sa bude volať periodicky
    # Zatiaľ túto logiku vypneme
    logger.debug("Rollover kontrola je teraz vypnutá - používame dynamický výber kontraktov")

    if config.CHECK_EOD_CLOSURE_ENABLED and utils.is_near_market_close():
        logger.info("EOD CHECK: Podmienka is_near_market_close() je TRUE. Kontrolujem pozície na uzavretie.")
        any_position_closed_eod_flag = False
        for inst_data_eod_item in active_instruments_to_process:
            symbol_eod_item = inst_data_eod_item['symbol']
            contract_eod_item = inst_data_eod_item.get('contract')

            # Najprv overíme skutočný stav pozície v IB
            current_ib_positions_eod = ib.positions()
            actual_position_size_eod = 0
            for p_eod in current_ib_positions_eod:
                if p_eod.contract.conId == contract_eod_item.conId:
                    actual_position_size_eod = p_eod.position
                    break

            if actual_position_size_eod != 0 and contract_eod_item:
                # Určíme správny smer uzatvárania na základe skutočnej pozície
                if actual_position_size_eod > 0:
                    action_eod_item = 'SELL'
                    position_type_eod = 'LONG'
                else:
                    action_eod_item = 'BUY'
                    position_type_eod = 'SHORT'

                # Použijeme skutočnú veľkosť pozície
                quantity_to_close = abs(actual_position_size_eod)

                logger.warning(f"[{symbol_eod_item}] EOD: Pozícia {position_type_eod} s veľkosťou {actual_position_size_eod} je otvorená. Pokúšam sa ju uzavrieť.")
                try:
                    eod_close_order_obj = MarketOrder(action_eod_item, quantity_to_close)
                    eod_close_order_obj.outsideRth = True
                    trade_eod_submission = ib.placeOrder(contract_eod_item, eod_close_order_obj)
                    status_eod_msg = trade_eod_submission.orderStatus.status if trade_eod_submission and trade_eod_submission.orderStatus else 'N/A'
                    logger.info(f"[{symbol_eod_item}] EOD: Príkaz na uzavretie zadaný. Stav: {status_eod_msg}")

                    utils.send_telegram(f"[{symbol_eod_item}] EOD: Príkaz na uzavretie pozície {position_type_eod} s veľkosťou {quantity_to_close} zadaný.")

                    ib.sleep(5)
                    exit_price_eod_val = inst_data_eod_item.get('entry_price', 0.0); pnl_eod_val = 0.0
                    all_fills_eod = ib.fills()
                    for fill_eod in reversed(all_fills_eod):
                        if fill_eod.contract.conId == contract_eod_item.conId:
                            fill_time_eod_str = fill_eod.execution.time; fill_time_eod_dt = None
                            try:
                                # Ak je už datetime objekt, použijeme ho priamo
                                if isinstance(fill_time_eod_str, datetime):
                                    fill_time_eod_dt = fill_time_eod_str.astimezone(pytz.utc) if fill_time_eod_str.tzinfo else pytz.utc.localize(fill_time_eod_str)
                                else:
                                    fill_time_eod_dt = datetime.strptime(fill_time_eod_str, '%Y%m%d  %H:%M:%S')
                                    gateway_tz_str = "America/New_York"
                                    fill_time_eod_dt = pytz.timezone(gateway_tz_str).localize(fill_time_eod_dt, is_dst=None).astimezone(pytz.utc)
                            except Exception as e_fill_time_eod:
                                logger.warning(f"[{symbol_eod_item}] EOD: Nepodarilo sa parsovať/konvertovať čas fillu: {fill_time_eod_str}, chyba: {e_fill_time_eod}")

                            # Kontrola či je fill z posledných 30 minút (EOD uzatváranie)
                            now_utc = datetime.now(pytz.utc)
                            if fill_time_eod_dt and (now_utc - fill_time_eod_dt).total_seconds() < 1800:  # 30 minút
                                if (fill_eod.execution.side == 'SLD' and position_type_eod == 'LONG') or \
                                   (fill_eod.execution.side == 'BOT' and position_type_eod == 'SHORT'):
                                    exit_price_eod_val = fill_eod.execution.price
                                    entry_p_eod = inst_data_eod_item.get('entry_price', exit_price_eod_val)
                                    if isinstance(entry_p_eod, (int,float)) and isinstance(exit_price_eod_val, (int,float)):
                                        pnl_pts_eod = (exit_price_eod_val - entry_p_eod) if position_type_eod == 'LONG' else (entry_p_eod - exit_price_eod_val)
                                        pnl_eod_val = pnl_pts_eod * inst_data_eod_item['multiplier'] * quantity_to_close
                                        logger.info(f"[{symbol_eod_item}] EOD uzavretie, cena z fills: {exit_price_eod_val}, PNL: {pnl_eod_val:.2f}")
                                    break

                    if utils.append_trade_to_csv(
                         symbol_eod_item, position_type_eod, inst_data_eod_item.get('entry_price',0.0),
                         exit_price_eod_val, pnl_eod_val,
                         inst_data_eod_item.get('entry_time','EOD Exit'), inst_data_eod_item.get('sl_price',0.0),
                         inst_data_eod_item.get('trailing_set', False)
                    ): logger.info(f"[{symbol_eod_item}] EOD obchod zapísaný do CSV.")
                    else: logger.error(f"[{symbol_eod_item}] EOD obchod sa NEPODARILO zapísať do CSV.")

                    # Resetujeme interný stav
                    inst_data_eod_item.update({'inPosition': False, 'trailing_set': False, 'sl_order_id': None, 'entry_signal': '', 'entry_price': 0.0, 'entry_time': '', 'entry_timestamp': 0.0})
                    if contract_eod_item.conId not in last_closed_conids_session:
                         last_closed_conids_session.add(contract_eod_item.conId)
                    any_position_closed_eod_flag = True
                except Exception as e_eod_inst_close_loop:
                    logger.error(f"[{symbol_eod_item}] Chyba pri EOD uzatváraní pozície: {e_eod_inst_close_loop}", exc_info=True)
            elif contract_eod_item:
                logger.debug(f"[{symbol_eod_item}] EOD: Žiadna otvorená pozícia na uzavretie (IB pozícia: {actual_position_size_eod}).")

        if not any_position_closed_eod_flag and any(inst.get('inPosition') for inst in active_instruments_to_process):
            logger.info("EOD: Neboli nájdené žiadne pozície na uzavretie, alebo už boli spracované skôr.")
        elif not any(inst.get('inPosition') for inst in active_instruments_to_process):
             logger.info("EOD: Žiadne otvorené pozície na uzavretie.")

    # HLAVNÁ OBCHODNÁ LOGIKA - spracovanie každého inštrumentu
    for inst_data in active_instruments_to_process:
        symbol = inst_data['symbol']
        contract = inst_data.get('contract')

        if not contract:
            logger.warning(f"[{symbol}] Preskakujem - chýba kontrakt")
            continue

        try:
            # Získanie denných dát pre výpočet Camarilla úrovní
            logger.debug(f"[{symbol}] Získavam denné dáta pre výpočet Camarilla úrovní...")
            daily_bars = ib.reqHistoricalData(
                contract, '', '5 D', '1 day',
                whatToShow='ASK', useRTH=False, formatDate=1
            )

            if not daily_bars or len(daily_bars) < 2:
                logger.warning(f"[{symbol}] Nedostatok denných dát ({len(daily_bars) if daily_bars else 0} sviečok). Preskakujem.")
                continue

            df_daily = util.df(daily_bars)
            df_daily = safe_process_dataframe_index(df_daily, symbol, "denné")
            if df_daily is None:
                logger.warning(f"[{symbol}] Nepodarilo sa spracovať denné dáta. Preskakujem.")
                continue

            # Výpočet Camarilla úrovní
            h4, l4 = utils.calc_pivots(df_daily, symbol)
            if h4 is None or l4 is None:
                logger.warning(f"[{symbol}] Nepodarilo sa vypočítať Camarilla úrovne. Preskakujem.")
                continue

            # Získanie hodinových dát pre signály
            logger.debug(f"[{symbol}] Získavam hodinové dáta pre signály...")
            hourly_bars = ib.reqHistoricalData(
                contract, '', '2 D', config.TIMEFRAME,
                whatToShow='ASK', useRTH=False, formatDate=1
            )

            if not hourly_bars or len(hourly_bars) < 10:
                logger.warning(f"[{symbol}] Nedostatok hodinových dát ({len(hourly_bars) if hourly_bars else 0} sviečok). Preskakujem.")
                continue

            df_hourly = util.df(hourly_bars)
            df_hourly = safe_process_dataframe_index(df_hourly, symbol, "hodinové")
            if df_hourly is None:
                logger.warning(f"[{symbol}] Nepodarilo sa spracovať hodinové dáta. Preskakujem.")
                continue

            # Výpočet EMA(8)
            df_hourly['ema8'] = df_hourly['close'].ewm(span=8, adjust=False).mean()

            # Získanie poslednej sviečky
            last_bar = df_hourly.iloc[-1]
            lc, lo, le = last_bar['close'], last_bar['open'], last_bar['ema8']

            # Formátovanie logov podľa typu inštrumentu
            price_format = ".4f" if symbol in ['M6A', 'M6B', 'M6E'] else ".2f"
            logger.info(f"[{symbol}] Posledná sviečka: O={lo:{price_format}} C={lc:{price_format}} EMA8={le:{price_format}} H4={h4:{price_format}} L4={l4:{price_format}}")

            # Výpočet signálov
            long_signal = lc > h4 and lo < h4 and lc > le
            short_signal = lc < l4 and lo > l4 and lc < le

            # Kontrola signálov a vstup do obchodu
            if long_signal and not inst_data.get('inPosition', False):
                logger.info(f"[{symbol}] LONG signál detekovaný! Zadávam bracket order...")
                success = ib_interface.place_bracket_order(ib, inst_data, 'LONG', lc)
                if success:
                    logger.info(f"[{symbol}] LONG bracket order úspešne zadaný")
                else:
                    logger.error(f"[{symbol}] LONG bracket order zlyhal")

            elif short_signal and not inst_data.get('inPosition', False):
                logger.info(f"[{symbol}] SHORT signál detekovaný! Zadávam bracket order...")
                success = ib_interface.place_bracket_order(ib, inst_data, 'SHORT', lc)
                if success:
                    logger.info(f"[{symbol}] SHORT bracket order úspešne zadaný")
                else:
                    logger.error(f"[{symbol}] SHORT bracket order zlyhal")

            elif inst_data.get('inPosition', False):
                logger.debug(f"[{symbol}] Už v pozícii ({inst_data.get('entry_signal', 'N/A')}), preskakujem signály")
            else:
                logger.debug(f"[{symbol}] Žiadny signál (LONG: {long_signal}, SHORT: {short_signal})")

        except Exception as e_inst_processing:
            logger.error(f"[{symbol}] Chyba pri spracovaní inštrumentu: {e_inst_processing}", exc_info=True)


if __name__ == '__main__':
    utils.setup_logging()
    logger.info(f"Štartuje Camarilla multi-inštrument bot, TF={config.TIMEFRAME}, PID={os.getpid()}")

    bot_crashed_flag_main_outer = False
    consecutive_crashes_main_outer_loop = 0

    try:
        current_active_expiry = "DYNAMIC"  # Nová logika - dynamický výber kontraktov
        logger.info("Inicializujem s dynamickým výberom najlepších kontraktov")
    except Exception as e_init_expiry_main_outer_loop:
        logger.critical(f"Nepodarilo sa inicializovať: {e_init_expiry_main_outer_loop}. Ukončujem.")
        sys.exit(1)

    while True:
        is_likely_gateway_restarting_flag_outer = False
        try:
            if not ib.isConnected():
                logger.info(f"Pokus o pripojenie k IB na {config.IB_HOST}:{config.IB_PORT} s ClientID {config.IB_CLIENT_ID}")
                max_attempts_for_connection_local_outer = getattr(config, 'MAX_GATEWAY_RESTART_CONNECT_ATTEMPTS', getattr(config, 'MAX_CONNECT_ATTEMPTS', 5))

                for attempt_idx_main_conn_loop_outer_loop in range(max_attempts_for_connection_local_outer):
                    try:
                        logger.info(f"Pokus o pripojenie č. {attempt_idx_main_conn_loop_outer_loop + 1}/{max_attempts_for_connection_local_outer}...")
                        ib.connect(config.IB_HOST, config.IB_PORT, clientId=config.IB_CLIENT_ID, timeout=20)
                        if ib.isConnected():
                            logger.info("Úspešne pripojený k IB.")
                            if needs_data_initialization or not instruments_data_list or not all(inst.get('contract') for inst in instruments_data_list):
                                logger.info("Volám initialize_instruments_data() z __main__ po pripojení.")
                                initialize_instruments_data()
                            is_likely_gateway_restarting_flag_outer = False
                            break
                    except ConnectionRefusedError as cre_main_inner_loop_outer_loop:
                        logger.warning(f"Pripojenie odmietnuté (pokus {attempt_idx_main_conn_loop_outer_loop+1}): {cre_main_inner_loop_outer_loop}. IB Gateway možno ešte nebeží.")
                        is_likely_gateway_restarting_flag_outer = True
                        wait_for_gateway_s_val_outer_loop_conn_refused = 30 + (attempt_idx_main_conn_loop_outer_loop * 30)
                        wait_for_gateway_s_val_outer_loop_conn_refused = min(wait_for_gateway_s_val_outer_loop_conn_refused, 300)
                        logger.info(f"Čakám {wait_for_gateway_s_val_outer_loop_conn_refused} sekúnd, kým sa IB Gateway spustí...")
                        time.sleep(wait_for_gateway_s_val_outer_loop_conn_refused)
                        if attempt_idx_main_conn_loop_outer_loop == max_attempts_for_connection_local_outer - 1:
                            logger.error(f"Všetkých {max_attempts_for_connection_local_outer} pokusov o pripojenie po ConnectionRefused zlyhalo.")
                            raise
                    except Exception as e_conn_main_loop_inner_final_conn_outer_loop_other_exc:
                        logger.error(f"Iná chyba pri pripájaní (pokus {attempt_idx_main_conn_loop_outer_loop+1}): {e_conn_main_loop_inner_final_conn_outer_loop_other_exc}")
                        if attempt_idx_main_conn_loop_outer_loop < max_attempts_for_connection_local_outer - 1:
                            time.sleep(10 * (attempt_idx_main_conn_loop_outer_loop + 1))
                        else:
                            raise

            if not ib.isConnected():
                 logger.critical("Stále nie sme pripojení k IB po všetkých pokusoch. Ukončujem túto iteráciu vonkajšej slučky.")
                 raise ConnectionError("Finálne zlyhanie pripojenia k IB v __main__.")

            run_main_trading_loop()

            consecutive_crashes_main_outer_loop = 0
            if bot_crashed_flag_main_outer:
                utils.send_telegram("✅ Bot je znova v prevádzke po predchádzajúcom reštarte.")
                bot_crashed_flag_main_outer = False

        except ConnectionError as ce_main_loop_final_outer_conn_err_outer_loop:
            consecutive_crashes_main_outer_loop += 1
            logger.error(f"Kritická chyba pripojenia alebo pripojenie odmietnuté v hlavnom cykle: {ce_main_loop_final_outer_conn_err_outer_loop}. Pokus o reštart č. {consecutive_crashes_main_outer_loop}.")
            if not bot_crashed_flag_main_outer:
                utils.send_telegram(f"🔥 Bot má vážny problém s pripojením: {ce_main_loop_final_outer_conn_err_outer_loop}. Reštartujem.")
                bot_crashed_flag_main_outer = True
            needs_data_initialization = True
        except KeyboardInterrupt:
            logger.info("Bot bol ukončený manuálne (KeyboardInterrupt). Uvoľňujem pripojenie.")
            utils.send_telegram("🛑 Bot bol manuálne zastavený.")
            break
        except Exception as e_global_main_outermost_final_loop_exc_outer_loop_final_exc:
            consecutive_crashes_main_outer_loop += 1
            error_message_global_crash_final_log_outer = f"❌ Globálna chyba v bote: {e_global_main_outermost_final_loop_exc_outer_loop_final_exc}. Pokus o reštart č. {consecutive_crashes_main_outer_loop}."
            logger.error(error_message_global_crash_final_log_outer, exc_info=True)
            if not bot_crashed_flag_main_outer:
                utils.send_telegram(error_message_global_crash_final_log_outer)
                bot_crashed_flag_main_outer = True
            needs_data_initialization = True

        wait_duration_secs_restart_main_final_loop_val_outer_loop_final = 10 * consecutive_crashes_main_outer_loop

        if is_likely_gateway_restarting_flag_outer and consecutive_crashes_main_outer_loop <= getattr(config, 'MAX_GATEWAY_RESTART_CONNECT_ATTEMPTS', getattr(config, 'MAX_CONNECT_ATTEMPTS', 5)) :
             wait_duration_secs_restart_main_final_loop_val_outer_loop_final = 5
             is_likely_gateway_restarting_flag_outer = False

        max_consecutive_crashes_conf_outer = getattr(config, 'MAX_CONSECUTIVE_CRASHES_LIMIT', 5)
        if consecutive_crashes_main_outer_loop > max_consecutive_crashes_conf_outer :
            wait_duration_secs_restart_main_final_loop_val_outer_loop_final = 300
            logger.warning(f"Bot spadol {consecutive_crashes_main_outer_loop} krát. Pred ďalším pokusom čakám {wait_duration_secs_restart_main_final_loop_val_outer_loop_final}s.")
            if consecutive_crashes_main_outer_loop == max_consecutive_crashes_conf_outer + 1:
                 utils.send_telegram(f"🔥 Bot opakovane padá! Čakám dlhšie pred reštartom.")

        logger.info(f"Čakám {wait_duration_secs_restart_main_final_loop_val_outer_loop_final} sekúnd pred ďalším pokusom o spustenie hlavnej slučky...")

        try:
            if ib.isConnected():
                ib.disconnect()
                logger.info("Dočasne odpojený od IB pred reštartovacím cyklom.")
        except Exception as e_disc_main_outermost_final_loop_exc_outer_loop_final_disc:
            logger.error(f"Chyba pri odpájaní pred reštartom: {e_disc_main_outermost_final_loop_exc_outer_loop_final_disc}")

        time.sleep(wait_duration_secs_restart_main_final_loop_val_outer_loop_final)
        logger.info("Pokúšam sa reštartovať hlavnú slučku bota...")

    # Definitívne odpojenie pri normálnom ukončení
    if ib.isConnected():
        ib.disconnect()
    logger.info("Bot bol ukončený.")
